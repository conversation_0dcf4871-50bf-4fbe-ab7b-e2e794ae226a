import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:note_x/ui/pages/folder/folder_tracking_helper.dart';
import 'package:pull_down_button/pull_down_button.dart';
import 'package:note_x/lib.dart';

class FolderMoreOptions extends StatelessWidget {
  final FolderModel folder;
  final TextEditingController editFolderController;
  final String icon;
  final bool isInDetailPage;
  final VoidCallback? onEnableSelectionMode;
  final VoidCallback? onAddNoteConfirm;
  final Function(String)? onEditFolderConfirm;
  final Function([bool?])? onDeleteConfirm;
  final int? folderNoteCount;

  const FolderMoreOptions(
      {Key? key,
      required this.folder,
      required this.editFolderController,
      required this.icon,
      required this.isInDetailPage,
      this.onEnableSelectionMode,
      this.onAddNoteConfirm,
      this.onEditFolderConfirm,
      this.folderNoteCount,
      this.onDeleteConfirm})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: HiveService().noteBox.listenable(),
      builder: (context, Box<NoteModel> box, _) {
        final currentNotesCount = box.values
            .where((note) => note.folderId == folder.backendId)
            .length;

        return PullDownButton(
          routeTheme: PullDownMenuRouteTheme(
            backgroundColor: context.colorScheme.mainNeutral,
            borderRadius: BorderRadius.circular(16.r),
            width: context.isTablet ? 200 : 200.w,
          ),
          itemBuilder: (context) => [
            PullDownMenuItem(
              title: S.current.add_folder,
              itemTheme: PullDownMenuItemTheme(
                  textStyle: TextStyle(
                color: context.colorScheme.mainPrimary,
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
              )),
              onTap: () async {
                // Show the folder selection bottom sheet
                MoveToFolderHelper.showBottomSheet(
                  context,
                  foldersToBeMovedIds: [folder.backendId],
                  jumpToFolderId: folder.backendId,
                );
              },
              iconWidget: SvgPicture.asset(
                Assets.icons.icMoveFolder,
                width: 18.w,
                height: 18.h,
                colorFilter: ColorFilter.mode(
                  context.colorScheme.mainPrimary,
                  BlendMode.srcIn,
                ),
              ),
            ),
            if (isInDetailPage)
              PullDownMenuItem(
                title: S.current.add_note,
                itemTheme: PullDownMenuItemTheme(
                    textStyle: TextStyle(
                  color: context.colorScheme.mainPrimary,
                  fontSize: context.isTablet ? 16 : 14.sp,
                  fontWeight: FontWeight.w500,
                )),
                onTap: () async {
                  onAddNoteConfirm?.call();
                },
                iconWidget: SvgPicture.asset(
                  Assets.icons.icAdd,
                  width: 18.w,
                  height: 18.h,
                  colorFilter: ColorFilter.mode(
                    context.colorScheme.mainPrimary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            if (isInDetailPage && currentNotesCount > 0)
              PullDownMenuItem(
                title: S.current.select,
                itemTheme: PullDownMenuItemTheme(
                    textStyle: TextStyle(
                  color: context.colorScheme.mainPrimary,
                  fontSize: context.isTablet ? 16 : 14.sp,
                  fontWeight: FontWeight.w500,
                )),
                onTap: () async {
                  onEnableSelectionMode?.call();
                },
                iconWidget: SvgPicture.asset(
                  Assets.icons.icSelectFolder,
                  width: 18.w,
                  height: 18.h,
                  colorFilter: ColorFilter.mode(
                    context.colorScheme.mainPrimary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            PullDownMenuItem(
              title: S.current.edit_name,
              itemTheme: PullDownMenuItemTheme(
                  textStyle: TextStyle(
                color: context.colorScheme.mainPrimary,
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
              )),
              onTap: () async {
                FolderTrackingHelper.onEditFolderTap();
                showCreateFolderDialog(
                  context,
                  controller: editFolderController,
                  onPressed: () async {
                    onEditFolderConfirm?.call(editFolderController.text);
                    if (isInDetailPage) Navigator.pop(context);
                  },
                  onClosed: () {
                    FolderTrackingHelper.onEditFolderCancel();
                  },
                  title: S.current.edit_name,
                  contentButton: S.current.save,
                  hintText: '',
                  initialValue: folder.folderName,
                );
              },
              iconWidget: SvgPicture.asset(
                Assets.icons.icEditReminderTitle,
                width: 18.w,
                height: 18.h,
                colorFilter: ColorFilter.mode(
                  context.colorScheme.mainPrimary,
                  BlendMode.srcIn,
                ),
              ),
            ),
            PullDownMenuItem(
              itemTheme: PullDownMenuItemTheme(
                  textStyle: TextStyle(
                color: context.colorScheme.themeWhite,
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w500,
              )),
              onTap: () {
                FolderTrackingHelper.onDeleteFolderTap();
                CommonDialogs.buildDeleteDialog(
                  context,
                  title: S.current.delete_this_folder,
                  content: S.current.all_note_in_folder,
                  headerImageAssetFile: Assets.icons.icMascottDelete,
                  isFolderDeleteDialog: true,
                  folderNoteCount: folderNoteCount ?? 0,
                  onPressedCancelButton: () {
                    FolderTrackingHelper.onDeleteFolderCancel();
                  },
                  onPressedDeleteButton: ([willDeleteNote]) async {
                    onDeleteConfirm?.call(willDeleteNote);
                  },
                );
              },
              title: S.current.delete,
              isDestructive: true,
              iconWidget: SvgPicture.asset(
                Assets.icons.icDelete,
                colorFilter: const ColorFilter.mode(
                  AppColors.primaryRed,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ],
          buttonBuilder: (context, showMenu) {
            return GestureDetector(
              onTap: () {
                FolderTrackingHelper.onMorePressed();
                showMenu();
              },
              child: SvgPicture.asset(
                icon,
                width: context.isTablet ? 24 : 24.w,
                height: context.isTablet ? 24 : 24.w,
                colorFilter: ColorFilter.mode(
                  context.colorScheme.mainPrimary,
                  BlendMode.srcIn,
                ),
              ),
            );
          },
        );
      },
    );
  }
}
