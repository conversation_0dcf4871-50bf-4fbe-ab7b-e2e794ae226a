import 'package:flutter/foundation.dart';
import 'package:note_x/lib.dart';

class MoveFolderCubit extends BaseCubit<MoveFolderState> {
  MoveFolderCubit() : super(const MoveFolderState());

  final FolderService _folderService = FolderService();
  String? _currentJumpToFolderId;

  /// Initialize and load folder hierarchy
  Future<void> initialize({String? jumpToFolderId}) async {
    // Store the jumpToFolderId for future use
    _currentJumpToFolderId = jumpToFolderId;

    debugPrint('MoveFolderCubit: Initializing with jumpToFolderId: $jumpToFolderId');

    emit(state.copyWith(
      oneShotEvent: MoveFolderOneShotEvent.loading,
      isLoading: true,
    ));

    try {
      final allFolders = HiveFolderService.getAllFolders();
      debugPrint('MoveFolderCubit: All folders count: ${allFolders.length}');

      // Debug: Print all folders and their parent relationships
      for (final folder in allFolders) {
        debugPrint('MoveFolderCubit: Folder ${folder.folderName} (${folder.backendId}) - parent: ${folder.parentFolderId}');
      }

      // Build hierarchy without using fixSubFolderIds
      final rootFolders = _buildFolderHierarchy(allFolders, jumpToFolderId);
      
      // Auto-select folder if jumpToFolderId is provided
      MoveFolderViewModel? selectedFolder;
      if (jumpToFolderId != null && jumpToFolderId.isNotEmpty) {
        selectedFolder = _findFolderById(rootFolders, jumpToFolderId);
      }

      emit(state.copyWith(
        oneShotEvent: MoveFolderOneShotEvent.success,
        rootFolders: rootFolders,
        selectedFolder: selectedFolder,
        isLoading: false,
      ));
    } catch (e) {
      debugPrint('MoveFolderCubit: Error initializing: $e');
      emit(state.copyWith(
        oneShotEvent: MoveFolderOneShotEvent.error,
        errorMessage: e.toString(),
        isLoading: false,
      ));
    }
  }

  /// Build folder hierarchy from flat list
  List<MoveFolderViewModel> _buildFolderHierarchy(
    List<FolderModel> allFolders,
    String? jumpToFolderId,
  ) {
    // Create a map for quick lookup
    final folderMap = <String, FolderModel>{};
    for (final folder in allFolders) {
      folderMap[folder.backendId] = folder;
    }

    // Find root folders and build hierarchy
    final rootFolders = <MoveFolderViewModel>[];
    
    for (final folder in allFolders) {
      if (folder.parentFolderId == null || 
          folder.parentFolderId!.isEmpty ||
          !folderMap.containsKey(folder.parentFolderId!)) {
        // This is a root folder
        final viewModel = _buildFolderViewModel(
          folder, 
          folderMap, 
          jumpToFolderId,
        );
        rootFolders.add(viewModel);
      }
    }

    // Sort by creation date (newest first)
    rootFolders.sort((a, b) => 
      MyUtils.convertToTimestamp(b.createdAt)
          .compareTo(MyUtils.convertToTimestamp(a.createdAt)));

    return rootFolders;
  }

  /// Build view model for a folder and its subfolders recursively
  MoveFolderViewModel _buildFolderViewModel(
    FolderModel folder,
    Map<String, FolderModel> folderMap,
    String? jumpToFolderId,
  ) {
    // Find subfolders
    final subfolders = <MoveFolderViewModel>[];
    for (final subfolder in folderMap.values) {
      if (subfolder.parentFolderId == folder.backendId) {
        final subViewModel = _buildFolderViewModel(
          subfolder, 
          folderMap, 
          jumpToFolderId,
        );
        subfolders.add(subViewModel);
      }
    }

    // Determine if this folder should be expanded
    final shouldExpand = _shouldExpandFolder(folder.backendId, jumpToFolderId, folderMap);

    if (shouldExpand) {
      debugPrint('MoveFolderCubit: Expanding folder ${folder.folderName} (${folder.backendId}) with ${subfolders.length} subfolders for jumpToFolderId: $jumpToFolderId');
    } else {
      debugPrint('MoveFolderCubit: NOT expanding folder ${folder.folderName} (${folder.backendId}) with ${subfolders.length} subfolders for jumpToFolderId: $jumpToFolderId');
    }

    return MoveFolderViewModel.fromFolderModel(
      folder,
      isExpanded: shouldExpand,
    ).copyWith(subfolders: subfolders);
  }

  /// Check if folder should be expanded based on jumpToFolderId
  bool _shouldExpandFolder(
    String folderBackendId,
    String? jumpToFolderId,
    Map<String, FolderModel> folderMap,
  ) {
    if (jumpToFolderId == null || jumpToFolderId.isEmpty) {
      return false;
    }

    // If this folder is the target folder, expand it to show its subfolders
    if (folderBackendId == jumpToFolderId) {
      return true;
    }

    // If this folder contains the target folder in its hierarchy, expand it
    // This will expand all parent folders leading to the target folder
    return _containsTargetFolder(folderBackendId, jumpToFolderId, folderMap);
  }

  /// Check if a folder contains the target folder in its hierarchy
  bool _containsTargetFolder(
    String folderBackendId,
    String targetFolderId,
    Map<String, FolderModel> folderMap,
  ) {
    final targetFolder = folderMap[targetFolderId];
    if (targetFolder == null) {
      debugPrint('MoveFolderCubit: Target folder $targetFolderId not found in folderMap');
      return false;
    }

    // Check if the target folder is a descendant of this folder
    String? currentParentId = targetFolder.parentFolderId;
    while (currentParentId != null && currentParentId.isNotEmpty) {
      if (currentParentId == folderBackendId) {
        debugPrint('MoveFolderCubit: Folder $folderBackendId contains target $targetFolderId');
        return true;
      }
      final parentFolder = folderMap[currentParentId];
      currentParentId = parentFolder?.parentFolderId;
    }

    return false;
  }

  /// Find folder by ID in the hierarchy
  MoveFolderViewModel? _findFolderById(
    List<MoveFolderViewModel> folders,
    String folderId,
  ) {
    for (final folder in folders) {
      if (folder.backendId == folderId) {
        return folder;
      }
      final found = _findFolderById(folder.subfolders, folderId);
      if (found != null) {
        return found;
      }
    }
    return null;
  }

  /// Select a folder
  void selectFolder(MoveFolderViewModel folder) {
    emit(state.copyWith(selectedFolder: folder));
  }

  /// Toggle folder expansion
  void toggleFolderExpansion(String folderId) {
    final updatedRootFolders = _toggleExpansionInList(state.rootFolders, folderId);
    emit(state.copyWith(rootFolders: updatedRootFolders));
  }

  /// Toggle expansion in folder list recursively
  List<MoveFolderViewModel> _toggleExpansionInList(
    List<MoveFolderViewModel> folders,
    String folderId,
  ) {
    return folders.map((folder) {
      if (folder.backendId == folderId) {
        return folder.copyWith(isExpanded: !folder.isExpanded);
      } else {
        return folder.copyWith(
          subfolders: _toggleExpansionInList(folder.subfolders, folderId),
        );
      }
    }).toList();
  }

  /// Create subfolder
  Future<void> createSubfolder({
    required String name,
    required String parentFolderId,
  }) async {
    emit(state.copyWith(
      oneShotEvent: MoveFolderOneShotEvent.loading,
      isLoading: true,
    ));

    try {
      await _folderService.createSubfolder(
        name: name,
        parentFolderId: parentFolderId,
      );

      // Refresh the folder hierarchy, preserving the jumpToFolderId
      await initialize(jumpToFolderId: _currentJumpToFolderId);

      emit(state.copyWith(
        oneShotEvent: MoveFolderOneShotEvent.subfolderCreated,
        isLoading: false,
      ));
    } catch (e) {
      debugPrint('MoveFolderCubit: Error creating subfolder: $e');
      emit(state.copyWith(
        oneShotEvent: MoveFolderOneShotEvent.error,
        errorMessage: e.toString(),
        isLoading: false,
      ));
    }
  }

  /// Move folders and notes to target folder
  Future<void> moveFoldersAndNotes({
    required List<String> folderIds,
    required List<NoteModel> notes,
    required String targetFolderId,
  }) async {
    emit(state.copyWith(
      oneShotEvent: MoveFolderOneShotEvent.loading,
      isLoading: true,
    ));

    try {
      // Move folders
      for (final folderId in folderIds) {
        await _folderService.bulkMoveNotesToFolder(
          targetFolderId: targetFolderId,
          folderIds: [folderId],
          noteIds: notes.map((note) => note.backendNoteId).toList(),
        );
      }

      // Move notes to target folder
      for (final note in notes) {
        await HiveFolderService.addNoteToFolder(
          note: note,
          folderBackendId: targetFolderId,
        );
      }

      emit(state.copyWith(
        oneShotEvent: MoveFolderOneShotEvent.folderMoved,
        isLoading: false,
      ));
    } catch (e) {
      debugPrint('MoveFolderCubit: Error moving folders: $e');
      emit(state.copyWith(
        oneShotEvent: MoveFolderOneShotEvent.error,
        errorMessage: e.toString(),
        isLoading: false,
      ));
    }
  }

  /// Test method to debug folder expansion logic
  void testFolderExpansion(String? jumpToFolderId) {
    debugPrint('=== Testing folder expansion logic ===');
    debugPrint('jumpToFolderId: $jumpToFolderId');

    final allFolders = HiveFolderService.getAllFolders();
    debugPrint('Total folders: ${allFolders.length}');

    // Create folder map
    final folderMap = <String, FolderModel>{};
    for (final folder in allFolders) {
      folderMap[folder.backendId] = folder;
      debugPrint('Folder: ${folder.folderName} (${folder.backendId}) - parent: ${folder.parentFolderId}');
    }

    // Test expansion logic for each folder
    for (final folder in allFolders) {
      final shouldExpand = _shouldExpandFolder(folder.backendId, jumpToFolderId, folderMap);
      debugPrint('${folder.folderName}: shouldExpand = $shouldExpand');
    }

    debugPrint('=== End test ===');
  }
}
