// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'folder_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

FolderDto _$FolderDtoFromJson(Map<String, dynamic> json) {
  return _FolderDto.fromJson(json);
}

/// @nodoc
mixin _$FolderDto {
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'folder_name')
  String get folderName => throw _privateConstructorUsedError;
  @JsonKey(name: 'parent_folder_id')
  String? get parentFolderId => throw _privateConstructorUsedError;
  String get path => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  @JsonKey(name: 'note_count')
  int get noteCount => throw _privateConstructorUsedError;
  List<FolderDto> get subfolders => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FolderDtoCopyWith<FolderDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FolderDtoCopyWith<$Res> {
  factory $FolderDtoCopyWith(FolderDto value, $Res Function(FolderDto) then) =
      _$FolderDtoCopyWithImpl<$Res, FolderDto>;
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'folder_name') String folderName,
      @JsonKey(name: 'parent_folder_id') String? parentFolderId,
      String path,
      int level,
      @JsonKey(name: 'note_count') int noteCount,
      List<FolderDto> subfolders});
}

/// @nodoc
class _$FolderDtoCopyWithImpl<$Res, $Val extends FolderDto>
    implements $FolderDtoCopyWith<$Res> {
  _$FolderDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? folderName = null,
    Object? parentFolderId = freezed,
    Object? path = null,
    Object? level = null,
    Object? noteCount = null,
    Object? subfolders = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      folderName: null == folderName
          ? _value.folderName
          : folderName // ignore: cast_nullable_to_non_nullable
              as String,
      parentFolderId: freezed == parentFolderId
          ? _value.parentFolderId
          : parentFolderId // ignore: cast_nullable_to_non_nullable
              as String?,
      path: null == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      noteCount: null == noteCount
          ? _value.noteCount
          : noteCount // ignore: cast_nullable_to_non_nullable
              as int,
      subfolders: null == subfolders
          ? _value.subfolders
          : subfolders // ignore: cast_nullable_to_non_nullable
              as List<FolderDto>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FolderDtoImplCopyWith<$Res>
    implements $FolderDtoCopyWith<$Res> {
  factory _$$FolderDtoImplCopyWith(
          _$FolderDtoImpl value, $Res Function(_$FolderDtoImpl) then) =
      __$$FolderDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'folder_name') String folderName,
      @JsonKey(name: 'parent_folder_id') String? parentFolderId,
      String path,
      int level,
      @JsonKey(name: 'note_count') int noteCount,
      List<FolderDto> subfolders});
}

/// @nodoc
class __$$FolderDtoImplCopyWithImpl<$Res>
    extends _$FolderDtoCopyWithImpl<$Res, _$FolderDtoImpl>
    implements _$$FolderDtoImplCopyWith<$Res> {
  __$$FolderDtoImplCopyWithImpl(
      _$FolderDtoImpl _value, $Res Function(_$FolderDtoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? folderName = null,
    Object? parentFolderId = freezed,
    Object? path = null,
    Object? level = null,
    Object? noteCount = null,
    Object? subfolders = null,
  }) {
    return _then(_$FolderDtoImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      folderName: null == folderName
          ? _value.folderName
          : folderName // ignore: cast_nullable_to_non_nullable
              as String,
      parentFolderId: freezed == parentFolderId
          ? _value.parentFolderId
          : parentFolderId // ignore: cast_nullable_to_non_nullable
              as String?,
      path: null == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      noteCount: null == noteCount
          ? _value.noteCount
          : noteCount // ignore: cast_nullable_to_non_nullable
              as int,
      subfolders: null == subfolders
          ? _value._subfolders
          : subfolders // ignore: cast_nullable_to_non_nullable
              as List<FolderDto>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FolderDtoImpl implements _FolderDto {
  const _$FolderDtoImpl(
      {this.id = '',
      @JsonKey(name: 'folder_name') this.folderName = '',
      @JsonKey(name: 'parent_folder_id') this.parentFolderId,
      this.path = '',
      this.level = 0,
      @JsonKey(name: 'note_count') this.noteCount = 0,
      final List<FolderDto> subfolders = const <FolderDto>[]})
      : _subfolders = subfolders;

  factory _$FolderDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$FolderDtoImplFromJson(json);

  @override
  @JsonKey()
  final String id;
  @override
  @JsonKey(name: 'folder_name')
  final String folderName;
  @override
  @JsonKey(name: 'parent_folder_id')
  final String? parentFolderId;
  @override
  @JsonKey()
  final String path;
  @override
  @JsonKey()
  final int level;
  @override
  @JsonKey(name: 'note_count')
  final int noteCount;
  final List<FolderDto> _subfolders;
  @override
  @JsonKey()
  List<FolderDto> get subfolders {
    if (_subfolders is EqualUnmodifiableListView) return _subfolders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_subfolders);
  }

  @override
  String toString() {
    return 'FolderDto(id: $id, folderName: $folderName, parentFolderId: $parentFolderId, path: $path, level: $level, noteCount: $noteCount, subfolders: $subfolders)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FolderDtoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.folderName, folderName) ||
                other.folderName == folderName) &&
            (identical(other.parentFolderId, parentFolderId) ||
                other.parentFolderId == parentFolderId) &&
            (identical(other.path, path) || other.path == path) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.noteCount, noteCount) ||
                other.noteCount == noteCount) &&
            const DeepCollectionEquality()
                .equals(other._subfolders, _subfolders));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, folderName, parentFolderId,
      path, level, noteCount, const DeepCollectionEquality().hash(_subfolders));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FolderDtoImplCopyWith<_$FolderDtoImpl> get copyWith =>
      __$$FolderDtoImplCopyWithImpl<_$FolderDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FolderDtoImplToJson(
      this,
    );
  }
}

abstract class _FolderDto implements FolderDto {
  const factory _FolderDto(
      {final String id,
      @JsonKey(name: 'folder_name') final String folderName,
      @JsonKey(name: 'parent_folder_id') final String? parentFolderId,
      final String path,
      final int level,
      @JsonKey(name: 'note_count') final int noteCount,
      final List<FolderDto> subfolders}) = _$FolderDtoImpl;

  factory _FolderDto.fromJson(Map<String, dynamic> json) =
      _$FolderDtoImpl.fromJson;

  @override
  String get id;
  @override
  @JsonKey(name: 'folder_name')
  String get folderName;
  @override
  @JsonKey(name: 'parent_folder_id')
  String? get parentFolderId;
  @override
  String get path;
  @override
  int get level;
  @override
  @JsonKey(name: 'note_count')
  int get noteCount;
  @override
  List<FolderDto> get subfolders;
  @override
  @JsonKey(ignore: true)
  _$$FolderDtoImplCopyWith<_$FolderDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
