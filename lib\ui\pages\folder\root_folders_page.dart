import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:note_x/base/base_page_state.dart';
import 'package:note_x/lib.dart';
import 'package:note_x/ui/pages/folder/cubit/root_folders_cubit.dart';
import 'package:note_x/ui/pages/folder/folder_tracking_helper.dart';
import 'package:note_x/ui/pages/folder/widget/common_folder_item_widget.dart';
import 'package:note_x/ui/pages/folder/widget/folder_view_data.dart';

class RootFoldersPage extends StatefulWidget {
  const RootFoldersPage({
    required this.unsyncedNotes,
    super.key,
  });

  /// List of notes that failed to sync with the backend
  final List<NoteModel> unsyncedNotes;

  @override
  State<RootFoldersPage> createState() => _RootFoldersPageState();
}

class _RootFoldersPageState
    extends BasePageStateDelegate<RootFoldersPage, RootFoldersCubit> {
  final TextEditingController _folderNameController = TextEditingController();

  @override
  void dispose() {
    _folderNameController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: HiveService().folderBox.listenable(),
      builder: (context, box, child) {
        final folders = _getSortedFolders(box);
        return Column(
          children: [
            _buildCreateRootFolderButton(context),
            Expanded(
              child: _buildFoldersList(context, folders),
            ),
          ],
        );
      },
    );
  }

  /// Returns a sorted list of folders by creation date (newest first)
  List<FolderModel> _getSortedFolders(Box<FolderModel> box) {
    final allFolders = box.values.toList();

    // First, get all root folders (folders without parents)
    final rootFolders = allFolders
        .where((folder) => folder.parentFolderId == null)
        .toList()
      ..sort((a, b) => MyUtils.convertToTimestamp(b.createdAt)
          .compareTo(MyUtils.convertToTimestamp(a.createdAt)));

    // Then, get all subfolders
    final subFolders = allFolders
        .where((folder) => folder.parentFolderId != null)
        .toList()
      ..sort((a, b) => MyUtils.convertToTimestamp(b.createdAt)
          .compareTo(MyUtils.convertToTimestamp(a.createdAt)));

    // Combine root folders and subfolders
    return [...rootFolders, ...subFolders];
  }

  /// Builds the create folder button at the top of the screen
  Widget _buildCreateRootFolderButton(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 6.h),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: AppColors.gradientCTABlue,
          begin: Alignment.centerRight,
          end: Alignment.centerLeft,
        ),
        borderRadius: BorderRadius.circular(54.r),
      ),
      child: AppCommonButton(
        width: double.infinity,
        height: cubit.appCubit.isTablet ? 64 : 64.h,
        textWidget: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              Assets.icons.icCreateFolder,
              width: 24.w,
              height: 24.h,
            ),
            AppConstants.kSpacingItemW8,
            CommonText(
              S.current.create_folder,
              style: TextStyle(
                height: 1,
                fontSize: cubit.appCubit.isTablet ? 18 : 16.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.white,
              ),
            ),
          ],
        ),
        onPressed: _showCreateFolderDialog,
      ),
    );
  }

  /// Shows the dialog to create a new folder
  void _showCreateFolderDialog() {
    FolderTrackingHelper.onCreateFolderTap();
    showCreateFolderDialog(
      context,
      onPressed: () {
        cubit.createRootFolder(_folderNameController.text.trim());
        _folderNameController.text = '';
      },
      onClosed: () {
        FolderTrackingHelper.onCreateFolderCancel();
      },
      title: S.current.create_new_folder,
      contentButton: S.current.create,
      hintText: S.current.required,
      controller: _folderNameController,
      initialValue: '',
    );
  }

  /// Builds the list of folders
  Widget _buildFoldersList(BuildContext context, List<FolderModel> folders) {
    return ListView.builder(
      padding: EdgeInsets.fromLTRB(
          16.w, 16.h, 16.w, MediaQuery.of(context).padding.bottom),
      itemCount: folders.length + (widget.unsyncedNotes.isNotEmpty ? 1 : 0),
      itemBuilder: (context, index) {
        // Show unsynced notes as the first item if there are any
        if (widget.unsyncedNotes.isNotEmpty && index == 0) {
          return _buildUnsyncedNotesItem();
        }

        // Calculate the actual folder index accounting for the unsynced notes item
        final folderIndex = index - (widget.unsyncedNotes.isNotEmpty ? 1 : 0);
        final folder = folders[folderIndex];
        final notesCount = _calculateNumberOfNotes(folder.backendId);

        return _buildFolderItem(folder, notesCount);
      },
    );
  }

  /// Builds the unsynced notes item that appears at the top of the list
  Widget _buildUnsyncedNotesItem() {
    final notesCount = widget.unsyncedNotes.length;
    final unsyncedFolder = FolderModel(
      folderName: S.current.unsynced_notes,
      backendId: '',
    );

    return CupertinoButton(
      alignment: Alignment.topCenter,
      padding: EdgeInsets.zero,
      onPressed: () => _navigateToUnsyncedNotes(unsyncedFolder),
      child: CommonFolderItemWidget(
          viewData: FolderViewData.unsyncedNotes(
              folderName: S.current.unsynced_notes, numberOfNotes: notesCount),
          isTablet: cubit.appCubit.isTablet),
    );
  }

  /// Navigates to the unsynced notes folder detail page
  void _navigateToUnsyncedNotes(FolderModel folder) {
    FolderTrackingHelper.onUnsyncedNoteTap();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FolderDetailPage(
          isUnsyncedFolder: true,
          folder: folder,
        ),
      ),
    );
  }

  /// Builds a regular folder item
  Widget _buildFolderItem(FolderModel folder, int notesCount) {
    return CupertinoButton(
      alignment: Alignment.topCenter,
      padding: EdgeInsets.zero,
      onPressed: () => _navigateToFolderDetail(folder),
      child: CommonFolderItemWidget(
          viewData: FolderViewData.fromFolderModel(
            folder: folder,
            numberOfNotes: folder.noteCount,
            subfoldersCount: folder.subfolders.length,
            onEditFolderConfirm: (newFolderName) {
              cubit.editFolder(newFolderName, folder);
            },
          ),
          isTablet: cubit.appCubit.isTablet),
    );
  }

  /// Navigates to the folder detail page
  void _navigateToFolderDetail(FolderModel folder) {
    FolderTrackingHelper.onFolderDetailTap();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FolderDetailPage(
          folder: folder,
        ),
      ),
    );
  }

  /// Calculates the number of notes in a folder based on its backend ID
  int _calculateNumberOfNotes(String folderBackendId) {
    final noteBox = HiveService().noteBox;
    final notesInFolder =
        noteBox.values.where((note) => note.folderId == folderBackendId);
    return notesInFolder.length;
  }
}
